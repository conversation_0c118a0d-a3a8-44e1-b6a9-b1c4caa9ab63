/* Modern Reset & Base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
  color: #1a1a1a;
  background-color: #ffffff;
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Playfair Display", serif;
  font-weight: 600;
  line-height: 1.2;
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-tag {
  display: inline-block;
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  color: white;
  padding: 8px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 16px;
}

.section-header h2 {
  font-size: 2.75rem;
  color: #1e293b;
  margin-bottom: 16px;
  font-weight: 700;
}

.section-description {
  font-size: 1.125rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.7;
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  z-index: 1000;
  transition: all 0.3s ease;
}

.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80px;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 40px;
  height: 40px;
}

.logo-text {
  font-family: "Playfair Display", serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: #2563eb;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 32px;
  align-items: center;
}

.nav-menu a {
  text-decoration: none;
  color: #4b5563;
  font-weight: 500;
  font-size: 15px;
  transition: all 0.3s ease;
  position: relative;
}

.nav-menu a:hover {
  color: #2563eb;
}

.nav-menu a::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: #2563eb;
  transition: width 0.3s ease;
}

.nav-menu a:hover::after {
  width: 100%;
}

.nav-cta-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.btn-distributor,
.btn-shop {
  padding: 10px 20px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.btn-distributor {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}

.btn-distributor:hover {
  background: #1d4ed8;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

.btn-shop {
  background: transparent;
  color: #2563eb;
  border-color: #2563eb;
}

.btn-shop:hover {
  background: #2563eb;
  color: white;
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background: #2563eb;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Hero Section */
.hero {
  position: relative;
  margin-top: 80px;
  min-height: 90vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(37, 99, 235, 0.05) 0%,
    rgba(59, 130, 246, 0.1) 100%
  );
  z-index: -1;
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(
      circle at 25% 25%,
      rgba(37, 99, 235, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(59, 130, 246, 0.08) 0%,
      transparent 50%
    );
  z-index: -1;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  padding: 12px 20px;
  border-radius: 30px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 24px;
  box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 20px;
  line-height: 1.1;
  background: linear-gradient(135deg, #1e293b 0%, #2563eb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: #64748b;
  margin-bottom: 40px;
  line-height: 1.6;
  max-width: 500px;
}

.hero-actions {
  display: flex;
  gap: 20px;
  margin-bottom: 50px;
}

.btn-primary,
.btn-secondary {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 16px 32px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.btn-primary {
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  color: white;
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(37, 99, 235, 0.4);
}

.btn-secondary {
  background: transparent;
  color: #2563eb;
  border-color: #2563eb;
}

.btn-secondary:hover {
  background: #2563eb;
  color: white;
  transform: translateY(-2px);
}

.hero-stats {
  display: flex;
  gap: 40px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 800;
  color: #2563eb;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-image-container {
  position: relative;
  width: 100%;
  max-width: 500px;
}

.hero-main-image {
  width: 100%;
  height: auto;
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

.hero-main-image:hover {
  transform: scale(1.02);
}

/* About Section */
.about-section {
  padding: 100px 0;
  background: #f8fafc;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.about-description {
  font-size: 1.125rem;
  color: #64748b;
  margin-bottom: 40px;
  line-height: 1.7;
}

.about-features {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-4px);
}

.feature-item i {
  font-size: 1.5rem;
  color: #2563eb;
  margin-top: 4px;
}

.feature-item h4 {
  font-size: 1.125rem;
  color: #1e293b;
  margin-bottom: 8px;
  font-weight: 600;
}

.feature-item p {
  color: #64748b;
  font-size: 14px;
  line-height: 1.5;
}

.about-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.world-map-container {
  position: relative;
  width: 100%;
  max-width: 500px;
  height: 400px;
  background: linear-gradient(135deg, #e0f2fe 0%, #f0f9ff 100%);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.export-route {
  position: absolute;
}

.india-to-malaysia {
  top: 50%;
  left: 20%;
  transform: translateY(-50%);
}

.india-to-usa {
  top: 30%;
  right: 20%;
}

.route-point {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  position: relative;
}

.route-point i {
  font-size: 1.5rem;
  color: #2563eb;
}

.route-point span {
  font-size: 12px;
  font-weight: 600;
  color: #1e293b;
}

.route-line {
  width: 100px;
  height: 2px;
  background: linear-gradient(90deg, #2563eb, #3b82f6);
  margin: 10px 0;
  position: relative;
}

.route-line::after {
  content: "";
  position: absolute;
  right: -6px;
  top: -4px;
  width: 0;
  height: 0;
  border-left: 8px solid #2563eb;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
}

.map-stats {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 40px;
}

.map-stat {
  text-align: center;
}

.map-stat .stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2563eb;
  display: block;
}

.map-stat .stat-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

/* Products Section */
.products-section {
  padding: 100px 0;
  background: white;
}

.products-carousel {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  margin-bottom: 60px;
}

.product-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #f1f5f9;
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.product-card.featured {
  border: 2px solid #2563eb;
  transform: scale(1.02);
}

.product-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.product-info {
  padding: 24px;
}

.product-info h3 {
  font-size: 1.375rem;
  color: #1e293b;
  margin-bottom: 12px;
  font-weight: 700;
}

.product-description {
  color: #64748b;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 20px;
}

.product-specs {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.spec-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.spec-item i {
  color: #2563eb;
  width: 16px;
}

.spec-item span {
  color: #4b5563;
}

.product-certifications {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.cert-badge {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  color: #1d4ed8;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.products-cta {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 60px;
}

/* Partnerships Section */
.partnerships-section {
  padding: 100px 0;
  background: #1e293b;
  color: white;
}

.partnership-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: start;
}

.partnership-info {
  background: rgba(255, 255, 255, 0.05);
  padding: 40px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.nandini-partnership {
  text-align: center;
}

.partnership-logo {
  margin-bottom: 30px;
}

.partnership-logo img {
  max-width: 200px;
  height: auto;
  border-radius: 12px;
}

.partnership-details h3 {
  font-size: 1.5rem;
  color: #fbbf24;
  margin-bottom: 24px;
  font-weight: 700;
}

.partnership-benefits {
  list-style: none;
  text-align: left;
}

.partnership-benefits li {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 0;
  color: #cbd5e1;
  font-size: 15px;
  line-height: 1.6;
}

.partnership-benefits i {
  color: #10b981;
  margin-top: 2px;
  font-size: 16px;
}

.export-workflow {
  background: white;
  color: #1e293b;
  padding: 40px;
  border-radius: 20px;
}

.export-workflow h3 {
  font-size: 1.5rem;
  color: #1e293b;
  margin-bottom: 30px;
  text-align: center;
  font-weight: 700;
}

.workflow-steps {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.workflow-step {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  transition: transform 0.3s ease;
}

.workflow-step:hover {
  transform: translateX(8px);
}

.step-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.step-content h4 {
  font-size: 1.125rem;
  color: #1e293b;
  margin-bottom: 8px;
  font-weight: 600;
}

.step-content p {
  color: #64748b;
  font-size: 14px;
  line-height: 1.6;
}

.workflow-arrow {
  text-align: center;
  color: #2563eb;
  font-size: 1.5rem;
  margin: 10px 0;
}

.workflow-cta {
  text-align: center;
  margin-top: 40px;
}

/* Quality & Trust Section */
.quality-trust-section {
  padding: 100px 0;
  background: #f8fafc;
}

.quality-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: start;
}

.certifications h3 {
  font-size: 1.5rem;
  color: #1e293b;
  margin-bottom: 30px;
  font-weight: 700;
}

.cert-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.cert-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.cert-item:hover {
  transform: translateY(-4px);
}

.cert-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.cert-info h4 {
  font-size: 1rem;
  color: #1e293b;
  margin-bottom: 4px;
  font-weight: 600;
}

.cert-info p {
  color: #64748b;
  font-size: 13px;
}

.testimonials h3 {
  font-size: 1.5rem;
  color: #1e293b;
  margin-bottom: 30px;
  font-weight: 700;
}

.testimonials-grid {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.testimonial-card {
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-4px);
}

.testimonial-content {
  margin-bottom: 20px;
}

.quote-icon {
  color: #2563eb;
  font-size: 1.5rem;
  margin-bottom: 12px;
}

.testimonial-content p {
  font-style: italic;
  font-size: 1rem;
  color: #4b5563;
  line-height: 1.7;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 16px;
}

.author-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.author-info strong {
  color: #1e293b;
  font-size: 1rem;
  display: block;
  margin-bottom: 4px;
}

.author-info span {
  color: #64748b;
  font-size: 14px;
  display: block;
  margin-bottom: 8px;
}

.rating {
  display: flex;
  gap: 2px;
}

.rating i {
  color: #fbbf24;
  font-size: 14px;
}

/* Contact Section */
.contact-section {
  padding: 100px 0 60px;
  background: #1e293b;
  color: white;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  margin-bottom: 60px;
}

.contact-header h2 {
  font-size: 2.75rem;
  margin-bottom: 20px;
  color: white;
  font-weight: 700;
}

.contact-header p {
  font-size: 1.125rem;
  margin-bottom: 40px;
  color: #cbd5e1;
  line-height: 1.6;
}

.office-locations {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.office-item {
  background: rgba(255, 255, 255, 0.05);
  padding: 24px;
  border-radius: 16px;
  backdrop-filter: blur(10px);
}

.office-item h4 {
  color: #fbbf24;
  font-size: 1.125rem;
  margin-bottom: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.office-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.office-details p {
  color: #cbd5e1;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
}

.office-details i {
  color: #2563eb;
  width: 16px;
}

.contact-form-container {
  background: white;
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.contact-form h3 {
  color: #1e293b;
  margin-bottom: 30px;
  font-size: 1.5rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.contact-form input,
.contact-form select,
.contact-form textarea {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 15px;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.contact-form input:focus,
.contact-form select:focus,
.contact-form textarea:focus {
  outline: none;
  border-color: #2563eb;
}

.contact-form textarea {
  grid-column: 1 / -1;
  resize: vertical;
  min-height: 100px;
}

.form-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 20px 0;
}

.form-checkbox input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.form-checkbox label {
  color: #64748b;
  font-size: 14px;
  cursor: pointer;
}

/* Footer Bottom */
.footer-bottom {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 40px;
  align-items: center;
  padding-top: 40px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-left {
  display: flex;
  justify-content: flex-start;
}

.social-links {
  display: flex;
  gap: 16px;
}

.social-links a {
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.social-links a:hover {
  background: #2563eb;
  transform: translateY(-2px);
}

.footer-center {
  text-align: center;
}

.footer-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 12px;
}

.footer-logo img {
  width: 32px;
  height: 32px;
}

.footer-logo span {
  font-family: "Playfair Display", serif;
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
}

.footer-center p {
  color: #cbd5e1;
  font-size: 14px;
  margin: 0;
}

.footer-right {
  display: flex;
  justify-content: flex-end;
}

.footer-links {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.footer-links a {
  color: #cbd5e1;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: white;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-container {
    gap: 60px;
  }

  .hero-title {
    font-size: 3.5rem;
  }

  .about-content,
  .partnership-content,
  .quality-content,
  .contact-content {
    gap: 60px;
  }
}

@media (max-width: 768px) {
  .nav-menu,
  .nav-cta-buttons {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .hero-container {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .hero-title {
    font-size: 2.75rem;
  }

  .hero-stats {
    justify-content: center;
    gap: 24px;
  }

  .about-content,
  .partnership-content,
  .quality-content,
  .contact-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .products-carousel {
    grid-template-columns: 1fr;
  }

  .cert-grid {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .footer-bottom {
    grid-template-columns: 1fr;
    gap: 24px;
    text-align: center;
  }

  .footer-left,
  .footer-right {
    justify-content: center;
  }

  .section-header h2 {
    font-size: 2.25rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .products-cta {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 16px;
  }

  .hero-title {
    font-size: 2.25rem;
  }

  .section-header h2 {
    font-size: 1.875rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 16px;
  }

  .social-links {
    justify-content: center;
  }

  .footer-links {
    justify-content: center;
    gap: 16px;
  }
}
